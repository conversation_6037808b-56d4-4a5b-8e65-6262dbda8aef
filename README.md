# bfmap

bfmap is a map tile server,it provides map tile by adpter to other map provoders, like google map, tianditu map, osm map.
it also has map cache for fast access and decrease api request.

## operation and flow

### 系统使用

系统安装后，进入 Setup 页面设置默认用户、初始单位。默认用户可以创建默认的地图提供商 Token，这个 Token 供所有没有配置自身地图提供商 Token 的用户使用。

Setup 完成之后，使用默认用户登录系统，创建其他需要的用户和单位,创建用户时会自动创建一个名为 Default 的关联 Project，用户可以在设置页面配置自己的地图提供商 Token,允许用户为同一个提供商配置多个Token。

用户可以创建多个单位，只能查看自身所属单位和创建单位的用户，可以修改自身创建单位的用户。

用户可以创建或多个 Project，每个 Project 都可以配置地图 API 的配额。Project 下可以有多个地图 Token，这些地图 Token 共享 Project 的配额，使用这些 Token 可以向系统请求地图数据。更新 Token 值时会先设置旧的 Token 状态为 Deleted,再添加一个新的 Token。

## 地图 API 使用方法

### 获取地图瓦片(Map Tile)

**描述:**

该接口用于获取指定坐标、缩放级别、地图类型和语言的地图瓦片图像。

**请求 URL:** `https://<host>:<port>/map`

**请求方法:** `GET`

**请求参数:**

| 参数名称         | 位置    | 类型      | 是否必选 | 描述                                                                                                                                                         |
|--------------|-------|---------|------|------------------------------------------------------------------------------------------------------------------------------------------------------------|
| `token`      | Query | String  | 是    | 用户身份验证令牌，用于授权访问 API。                                                                                                                                       |
| `x`          | Query | Integer | 是    | 瓦片 X 轴坐标。                                                                                                                                                  |
| `y`          | Query | Integer | 是    | 瓦片 Y 轴坐标。                                                                                                                                                  |
| `z`          | Query | Integer | 是    | 瓦片缩放级别 (Zoom level)。                                                                                                                                       |
| `mtype`      | Query | String  | 可选   | 地图类型 (Map Type)。支持`roadmap`、`satellite`和`hybrid`。 默认为 `roadmap`。                                                                                           |
| `lang`       | Query | String  | 可选   | 语言 (Language)。用于地图元素文本的语言，查看[google 地图支持的语言](https://developers.google.com/maps/faq?hl=zh-cn#languagesupport)。 默认为地图提供商的默认语言，google为en，osm和tianditu不可选择语言。 |
| `provider`   | Query | String  | 可选   | 地图服务提供商 (Map Provider): `google`、`tianditu`和`osm(只支持roadmap)`等。 默认为与token关联的provider 。                                                                     |
| `gcj02`      | Query | Integer | 可选   | 0/1,是否启用转换地图wgs84坐标为gcj02坐标，默认为0，不转换。**只对google地图且是国内坐标有效**                                                                                                |
| `sysname`    | Query | String  | 是    | 系统名称，用于验证系统是否允许使用当前token。                                                                                                                                  |
| `sysexpired` | Query | String  | 可选   | 系统过期标记，如果为1，表示系统过期，只会返回bfmap已缓存的瓦片和本地目录瓦片，不会向在线地图提供商请求。默认为0。                                                                                            |

**响应:**

- 成功 (200 OK):
  - Content-Type: `image/png`, `image/jpeg`
  - Body: 包含地图瓦片的二进制图像数据, 返回图片大小为 256x256。
- 错误 (400~600):
  - Body: 错误原因，字符串。

**注意事项:**

- 将 `<host>` 和 `<port>` 替换为实际的 API 服务器地址和端口。
- `token` 参数需要替换为用户实际的 API 令牌。
- `mtype`, `lang`，`provider`参数是可选的，如果省略，服务器会使用默认值。
- 错误响应的 JSON 结构和具体错误信息可能会根据实际 API 的实现而有所不同。
- `tianditu`的`lang`只有中文，`osm`为对应位置的语言，如中国是中文，美国是英文。

**示例:**

```url
https://localhost:2243/map?token=xxxxxxx&x=123&y=456&z=7&mtype=roadmap&lang=zh-CN&provider=google&sysname=xxxxx
```

## 开发帮助

### 前后端交互

后端启动时需要添加 `-debug`参数，否则无法跨域访问。

使用`connectrpc`进行前后端交互。有两个rpc服务，一个是`bfmap`，另一个是`ProtoDbSrv`。bfmap服务参考`bfmap.proto`，ProtoDbSrv服务参考`protodb.proto`。ProtoDbSrv服务是一个数据库服务，提供了对数据库的基本操作，每个表都有对应的操作权限，具体权限在[dbrpc](rpc/dbrpc.go#L303)的`CreateDbRpcHandler`函数中定义, ProtoDbSrv服务需要`Session-Id`才能访问。

### ssl证书

使用 mkcert 生成自签名证书。mkcert 是一个简单的工具，可以为本地开发生成自签名的 SSL 证书。它会自动创建和安装根证书，并为指定的域名生成证书。

```bash
yay -S mkcert
# 安装 mkcert 根证书到系统信任存储
# 这一步只需要执行一次，之后生成的证书会自动信任
mkcert -install
# 生成证书
mkcert -cert-file localhost.crt -key-file localhost.key localhost 127.0.0.1 ::1
```

mkcert生成的证书可以直接使用，不需要时删除即可。`mkcert -install`安装的根证书可以使用`mkcert -uninstall`卸载。

### 后端调试日志开启方法

- `debug` 调试日志总开关标志
- `debugRpc` rpc请求调试日志开关标志，开启后，所有rpc请求和响应都会打印出来
- `debugMap` 地图http请求调试日志开关，开启后，/map的请求和响应都会打印出来

示例：如果需要开启rpc调试日志，设置`-debug -debugRpc`。

### nats 地图瓦片请求

收到地图图片请求且bbolt没有数据，根据`setting.ini`配置，向NATS发送请求，超时后向地图提供商发起请求。
主题是`map.req`, 消息是 [MapReq](maps/mapreq.go#L12)，消息格式是json。
订阅主题，收到消息后调用[QueryTileFromKeyValueDb](maps/dbcache.go#L34)查找，回复消息是地图瓦片数据。
