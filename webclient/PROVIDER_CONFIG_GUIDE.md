# Provider 配置界面使用指南

## 概述

Provider 配置界面允许用户管理地图提供商的语言设置和本地目录配置。此功能专门为本地目录类型的地图提供商设计，帮助用户优化地图瓦片的语言显示和本地存储管理。

## 功能特性

### 1. 语言设置
- **支持多语言选择**：包括中文、英文、日文、法文、德文、西班牙文、葡萄牙文、俄文、韩文、阿拉伯文等
- **Provider 特定支持**：
  - Google Maps：支持所有语言
  - 天地图：仅支持中文 (zh-CN)
  - OpenStreetMap：不支持多语言设置（由地理位置决定）
- **仅对本地目录有效**：语言设置只影响本地目录中的地图瓦片

### 2. 本地目录设置
- **路径配置**：设置地图瓦片的本地存储目录路径
- **路径验证**：支持 Unix/Linux 和 Windows 路径格式验证
- **实时预览**：显示当前配置的路径信息

## 使用方法

### 访问配置界面
1. 登录系统后，在左侧导航菜单中找到 "Provider配置"
2. 点击进入配置管理页面

### 配置语言设置
1. 在 "语言设置" 卡片中，点击语言选择下拉框
2. 从支持的语言列表中选择所需语言
3. 查看语言支持说明，了解不同 Provider 的语言兼容性
4. 点击 "确认" 保存设置

### 配置本地目录
1. 在 "本地目录设置" 卡片中，输入本地目录的完整路径
2. 支持的路径格式：
   - Unix/Linux: `/path/to/map/tiles`
   - Windows: `C:\path\to\map\tiles` 或 `C:/path/to/map/tiles`
3. 系统会自动验证路径格式的有效性
4. 点击 "确认" 保存设置

### 重置配置
- 点击 "重置为默认" 按钮可以将所有配置恢复到默认状态
- 系统会要求确认操作以防止误操作

## 在 Map Provider Token 中使用

当创建或编辑本地目录类型的 Map Provider Token 时：

1. 选择本地目录类型的 Provider：
   - Google Maps: 本地目录
   - 天地图: 本地目录  
   - OpenStreetMap: 本地目录

2. 系统会自动显示语言设置字段

3. 可以为每个 Token 单独设置语言偏好

4. 语言设置会影响该 Token 对应的地图瓦片语言显示

## 技术实现

### 数据存储
- 配置数据使用 SessionStorage 进行本地持久化
- 支持响应式数据更新
- 配置变更会自动保存时间戳

### 语言标准
- 使用 BCP 47 语言标签格式
- 支持语言代码验证
- 兼容现有的语言处理逻辑

### 路径验证
- 支持多种操作系统的路径格式
- 实时验证路径有效性
- 防止无效路径配置

## 注意事项

1. **语言设置限制**：
   - 语言设置仅对本地目录类型的 Provider 有效
   - 不同 Provider 的语言支持能力不同
   - 天地图仅支持中文，OSM 不支持多语言

2. **本地目录要求**：
   - 确保指定的目录路径存在且可访问
   - 目录结构应符合地图瓦片的标准格式
   - 路径应具有适当的读取权限

3. **配置生效**：
   - 配置更改会立即保存到本地存储
   - 新配置在下次创建 Provider Token 时生效
   - 现有 Token 的配置不会自动更新

## 故障排除

### 常见问题
1. **语言设置不生效**：检查 Provider 类型是否为本地目录类型
2. **路径验证失败**：确认路径格式符合操作系统要求
3. **配置丢失**：检查浏览器是否清除了 SessionStorage

### 支持信息
- 配置界面支持中英文双语
- 所有操作都有相应的成功/失败提示
- 配置状态实时显示在界面上
