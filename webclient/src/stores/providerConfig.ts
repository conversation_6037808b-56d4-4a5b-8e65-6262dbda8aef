import { defineStore } from "pinia";
import { computed } from "vue";
import { useRefSessionStorage } from "./common";

// Provider 配置接口
export interface ProviderConfig {
  // 语言偏好设置
  languagePreference: string;
  // 本地目录路径设置
  localDirectoryPath: string;
  // 最后更新时间
  lastUpdated: number;
}

// 默认配置
const defaultConfig: ProviderConfig = {
  languagePreference: "",
  localDirectoryPath: "",
  lastUpdated: 0,
};

// 支持的语言选项
export const supportedLanguages = [
  { label: "中文 (zh-CN)", value: "zh-CN", code: "zh-CN" },
  { label: "English (en-US)", value: "en-US", code: "en-US" },
  { label: "日本語 (ja-JP)", value: "ja-JP", code: "ja-JP" },
  { label: "Français (fr-FR)", value: "fr-FR", code: "fr-FR" },
  { label: "Deutsch (de-DE)", value: "de-DE", code: "de-DE" },
  { label: "Español (es-ES)", value: "es-ES", code: "es-ES" },
  { label: "Português (pt-BR)", value: "pt-BR", code: "pt-BR" },
  { label: "Русский (ru-RU)", value: "ru-RU", code: "ru-RU" },
  { label: "한국어 (ko-KR)", value: "ko-KR", code: "ko-KR" },
  { label: "العربية (ar-SA)", value: "ar-SA", code: "ar-SA" },
];

// Provider 语言支持信息
export const providerLanguageSupport = {
  google: {
    name: "Google Maps",
    supportsMultiLanguage: true,
    supportedLanguages: supportedLanguages.map((lang) => lang.code),
    note: "Supports multiple languages",
  },
  tianditu: {
    name: "Tianditu",
    supportsMultiLanguage: false,
    supportedLanguages: ["zh-CN"],
    note: "Only supports Chinese (zh-CN)",
  },
  osm: {
    name: "OpenStreetMap",
    supportsMultiLanguage: false,
    supportedLanguages: [],
    note: "Language determined by geographic location",
  },
};

export const useProviderConfigStore = defineStore("providerConfig", () => {
  // 使用响应式 SessionStorage 存储配置
  const { value: config, setData: setConfig } =
    useRefSessionStorage<ProviderConfig>("providerConfig", defaultConfig);

  // 计算属性
  const hasLanguagePreference = computed(
    () => !!config.value.languagePreference
  );
  const hasLocalDirectoryPath = computed(
    () => !!config.value.localDirectoryPath
  );
  const isConfigured = computed(
    () => hasLanguagePreference.value || hasLocalDirectoryPath.value
  );

  // 获取当前语言显示名称
  const currentLanguageLabel = computed(() => {
    if (!config.value.languagePreference) return "";
    const lang = supportedLanguages.find(
      (l) => l.code === config.value.languagePreference
    );
    return lang ? lang.label : config.value.languagePreference;
  });

  // 设置语言偏好
  const setLanguagePreference = (language: string) => {
    const newConfig = {
      ...config.value,
      languagePreference: language,
      lastUpdated: Date.now(),
    };
    setConfig(newConfig);
  };

  // 设置本地目录路径
  const setLocalDirectoryPath = (path: string) => {
    const newConfig = {
      ...config.value,
      localDirectoryPath: path,
      lastUpdated: Date.now(),
    };
    setConfig(newConfig);
  };

  // 更新完整配置
  const updateConfig = (newConfig: Partial<ProviderConfig>) => {
    const updatedConfig = {
      ...config.value,
      ...newConfig,
      lastUpdated: Date.now(),
    };
    setConfig(updatedConfig);
  };

  // 重置配置为默认值
  const resetConfig = () => {
    setConfig({ ...defaultConfig });
  };

  // 验证路径格式
  const validatePath = (path: string): boolean => {
    if (!path) return true; // 空路径是有效的

    // 基本路径格式验证
    // 支持 Unix/Linux 路径格式 (/path/to/directory)
    // 支持 Windows 路径格式 (C:\path\to\directory 或 C:/path/to/directory)
    const unixPathRegex = /^\/[^<>:"|?*]*$/;
    const windowsPathRegex = /^[a-zA-Z]:[\\\/][^<>:"|?*]*$/;
    const relativePathRegex = /^[^<>:"|?*\/\\][^<>:"|?*]*$/;

    return (
      unixPathRegex.test(path) ||
      windowsPathRegex.test(path) ||
      relativePathRegex.test(path)
    );
  };

  // 验证语言代码格式
  const validateLanguage = (language: string): boolean => {
    if (!language) return true; // 空语言是有效的

    // BCP 47 语言标签格式验证
    const bcp47Regex = /^[a-z]{2,3}(-[A-Z]{2})?(-[a-z]{4})?(-[A-Z]{2}|\d{3})?$/;
    return bcp47Regex.test(language);
  };

  // 获取配置摘要信息
  const getConfigSummary = computed(() => {
    return {
      language: config.value.languagePreference || "Not set",
      languageLabel: currentLanguageLabel.value || "Not set",
      path: config.value.localDirectoryPath || "Not set",
      lastUpdated: config.value.lastUpdated
        ? new Date(config.value.lastUpdated).toLocaleString()
        : "Never",
      isConfigured: isConfigured.value,
    };
  });

  return {
    // 状态
    config,

    // 计算属性
    hasLanguagePreference,
    hasLocalDirectoryPath,
    isConfigured,
    currentLanguageLabel,
    getConfigSummary,

    // 方法
    setLanguagePreference,
    setLocalDirectoryPath,
    updateConfig,
    resetConfig,
    validatePath,
    validateLanguage,
  };
});
