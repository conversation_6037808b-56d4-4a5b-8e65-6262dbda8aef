import { useServerSetupStore } from "@/stores/serverSetup";
import { useLoginStatusStore } from "@/stores/session";
import { logger } from "@/utils/logger";
import { storeToRefs } from "pinia";
import type { RouteLocationRaw, RouteRecordRaw } from "vue-router";
import { createRouter, createWebHashHistory } from "vue-router";
import { i18n } from "./i18n/i18n";

// 1. 定义路由组件.
import MyLogin from "@/layouts/MyLogin.vue";
import { computed } from "vue";

function firstCharToLowerCase(str: string): string {
  const [first, ...rest] = str;
  return first.toLowerCase() + rest.join("");
}

function firstCharToUpperCase(str: string): string {
  const [first, ...rest] = str;
  return first.toUpperCase() + rest.join("");
}

// 自动加载pages目录下的组件作为MyLayout组件的子级路由页面
const pages = import.meta.glob("@/pages/**/*.vue");
const MyLayoutChildren: RouteRecordRaw[] = [];
const tempRoutes: any[] = [];
// 根据文件名猜测图标
const pageIcons: Record<string, string> = {
  Dashboard: "dashboard",
  DbOrg: "corporate_fare",
  DbUser: "people",
  DbProject: "assignment",
  DbProjectToken: "api",
  DbMapProviderToken: "api",
  MapGl: "map",
};
// 路由排序
const pageOrder: Record<string, number> = {
  Dashboard: 0,
  DbMapProviderToken: 1,
  DbProject: 2,
  DbOrg: 3,
  DbUser: 4,
  MapGl: 5,
};
for (const path in pages) {
  const page = pages[path];
  const fileName: string = path.split("/").pop() ?? path;
  const name = fileName.replace(/(.*)\.vue$/, "$1");
  const p = firstCharToLowerCase(name);
  const icon = pageIcons[name] ?? "description";

  tempRoutes.push({
    path: `/${p}`,
    name: firstCharToUpperCase(name),
    component: page,
    meta: {
      // 主页导航菜单生成配置参数
      nav: true,
      icon,
      title: computed(() =>
        i18n.global.te(`pages.${p}`) ? i18n.global.t(`pages.${p}`) : name
      ),
    },
    order: pageOrder[name] !== undefined ? pageOrder[name] : 100,
  });
}

tempRoutes.sort((a, b) => a.order - b.order);
MyLayoutChildren.push(...tempRoutes);
// 2. 定义一些路由
// 每个路由都需要映射到一个组件。
// 我们后面再讨论嵌套路由。
const routes: RouteRecordRaw[] = [
  {
    path: "/",
    name: "Layout",
    component: () => import("@/layouts/MyLayout.vue"),
    children: [
      {
        path: "",
        name: "redirect",
        redirect: (): RouteLocationRaw => {
          // 找到第一个子路由的路径
          const firstChild = MyLayoutChildren[0];
          return firstChild ? firstChild.path : "/dashboard";
        },
      },
      ...MyLayoutChildren,
    ],
  },
  { path: "/login", name: "Login", component: MyLogin },
  {
    path: "/setup",
    name: "Setup",
    component: () => import("@/layouts/MySetup.vue"),
  },
  {
    path: "/:pathMatch(.*)*",
    name: "NotFound",
    component: () => import("@/layouts/MyError404.vue"),
  }, // 匹配所有未定义路由的404页面
];

// 3. 创建路由实例并传递 `routes` 配置
// 你可以在这里导入 history 选项详细信息并在稍后说明
const router = createRouter({
  // history: createWebHistory(), // 使用 HTML5 History API (推荐)
  history: createWebHashHistory(), // 使用 hash 模式 (兼容性更好)
  routes, // `routes: routes` 的简写
});

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // to: 即将要进入的目标 路由对象
  // from: 当前导航正要离开的路由
  // next: 调用该方法后，才能进入下一个钩子
  logger.log("from:", from.path, "to:", to.path);
  const serverSetupStore = useServerSetupStore();
  const { alreadySetup, isInitSetupCodePending } =
    storeToRefs(serverSetupStore);
  logger.log("isInitSetupCodePending:", isInitSetupCodePending.value);
  // 还在初始化setup状态，等待结果
  if (isInitSetupCodePending.value) {
    while (isInitSetupCodePending.value) {
      await new Promise((resolve) => setTimeout(resolve, 100));
    }
  }
  logger.log("alreadySetup:", alreadySetup.value);
  // 如果没有设置过服务器，则跳转到 setup 页面
  if (!alreadySetup.value) {
    if (to.path !== "/setup") {
      next({ path: "/setup" });
    } else {
      next();
    }
    return;
  }

  // 已经配置过服务器，如果尝试跳转到setup页面，则重定向到首页
  if (to.path === "/setup") {
    next({ path: "/" });
    return;
  }

  // 如果没有登录，则跳转到登录页面
  const loginStatusStore = useLoginStatusStore();
  const { isLogin } = storeToRefs(loginStatusStore);
  logger.log("isLogin:", isLogin.value);
  if (!isLogin.value) {
    if (to.path !== "/login") {
      next({ path: "/login" });
    } else {
      next();
    }
    return;
  }

  next();
});

/*// 全局后置钩子
router.afterEach((to, from) => {
  // 路由切换后的一些操作，例如页面统计
  console.log('After navigation:', to, from)
})*/

export default router;
