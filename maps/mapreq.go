package maps

import (
	"fmt"
)

type MapReqResult struct {
	ImageBytes []byte
	Err        error
}

type MapReq struct {
	X int `json:"x"`
	Y int `json:"y"`
	Z int `json:"z"`
	// For Google Maps in China, whether to enable gcj02 conversion, 0: do not convert, 1: convert
	EnableGcj02    int     `json:"gcj02"`
	WgsLon, WgsLat float64 `json:"-"`
	GcjLon, GcjLat float64 `json:"-"`
	MapType        string  `json:"mapType"`
	Lang           string  `json:"lang"`
	Provider       int     `json:"provider"`
	ImageFormat    string  `json:"-"`
	// 1:png 2:jpg
	ImageFormatInt int    `json:"imageFormatInt"`
	Region         string `json:"-"`
	IsOutOfChina   bool   `json:"-"`
}

func (r *MapReq) IsRoadMap() bool {
	return r.MapType == MapTypeRoadmap
}

func (r *MapReq) IsSatelliteHybrid() bool {
	return r.MapType == MapTypeSatellite || r.MapType == MapTypeHybrid
}

func (r *MapReq) IsNeedTiandituMarkerForGoogleMap() bool {
	return r.MapType == MapTypeHybrid && (!r.IsOutOfChina || r.EnableGcj02 == 1)
}

func (r *MapReq) Clone() *MapReq {
	return &MapReq{
		X:              r.X,
		Y:              r.Y,
		Z:              r.Z,
		EnableGcj02:    r.EnableGcj02,
		WgsLon:         r.WgsLon,
		WgsLat:         r.WgsLat,
		GcjLon:         r.GcjLon,
		GcjLat:         r.GcjLat,
		MapType:        r.MapType,
		Lang:           r.Lang,
		Provider:       r.Provider,
		ImageFormat:    r.ImageFormat,
		ImageFormatInt: r.ImageFormatInt,
		Region:         r.Region,
	}
}

func (r *MapReq) CalcParams() {
	r.CalcLonLat()
	r.ParseIsOutOfChina()
	r.ParseImageFormat()
}

func (r *MapReq) CalcLonLat() {
	minLat, minLon, maxLat, maxLon := GlobalMapsManager.Mercator.GoogleTile2LatLonBounds(r.X, r.Y, r.Z)
	r.WgsLat = (minLat + maxLat) / 2
	r.WgsLon = (minLon + maxLon) / 2
	r.GcjLat, r.GcjLon = WGStoGCJ(r.WgsLat, r.WgsLon)
}

func (r *MapReq) ParseIsOutOfChina() {
	r.IsOutOfChina = isOutOFChina(r.WgsLat, r.WgsLon)
}

func (r *MapReq) ParseImageFormat() {
	if r.ImageFormat == TileImageFormatPng {
		r.ImageFormatInt = 1
	} else {
		r.ImageFormatInt = 2
	}
}

func (r *MapReq) KeyWithoutLangAndGcj01() string {
	return fmt.Sprintf("%d:%d:%d:%s:%d",
		r.X, r.Y, r.Z, r.MapType, r.Provider)
}

func (r *MapReq) Key() string {
	return fmt.Sprintf("%d:%d:%d:%s:%d:%s:%d",
		r.X, r.Y, r.Z, r.MapType, r.Provider, r.Lang, r.EnableGcj02)
}
func (r *MapReq) CacheKey(ext string) string {
	return fmt.Sprintf("%d:%d:%d:%s:%d:%s:%d-%s",
		r.X, r.Y, r.Z, r.MapType, r.Provider, r.Lang, r.EnableGcj02, ext)
}

func (r *MapReq) BlockKey() string {
	return r.CacheKey("block")
}
