package maps

import (
	"bfmap/dbproto"
	"testing"
)

func TestCalculateLocalDirectoryTileCoordinates(t *testing.T) {
	// Initialize GlobalMapsManager for testing
	if GlobalMapsManager.Mercator == nil {
		err := GlobalMapsManager.Init()
		if err != nil {
			t.Fatalf("Failed to initialize GlobalMapsManager: %v", err)
		}
	}

	tests := []struct {
		name           string
		provider       dbproto.MapProviderEnum
		x, y, z        int
		enableGcj02    int
		isOutOfChina   bool
		wgsLat, wgsLon float64
		gcjLat, gcjLon float64
		expectedX      int
		expectedY      int
		description    string
	}{
		{
			name:         "Google Maps in China with GCJ-02 enabled",
			provider:     dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
			x:            123, y: 456, z: 10,
			enableGcj02:  1,
			isOutOfChina: false,
			wgsLat:       39.9042, wgsLon: 116.4074, // Beijing WGS-84
			gcjLat:       39.9042 + 0.001, gcjLon: 116.4074 + 0.001, // Simulated GCJ-02 offset
			expectedX:    -1, expectedY: -1, // Will be calculated from GCJ-02 to WGS-84 conversion
			description:  "Should convert GCJ-02 request back to WGS-84 for local directory lookup",
		},
		{
			name:         "Google Maps in China with GCJ-02 disabled",
			provider:     dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
			x:            123, y: 456, z: 10,
			enableGcj02:  0,
			isOutOfChina: false,
			wgsLat:       39.9042, wgsLon: 116.4074,
			gcjLat:       39.9042 + 0.001, gcjLon: 116.4074 + 0.001,
			expectedX:    123, expectedY: 456,
			description: "Should use WGS-84 coordinates for local directory",
		},
		{
			name:         "Google Maps outside China",
			provider:     dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
			x:            123, y: 456, z: 10,
			enableGcj02:  1,
			isOutOfChina: true,
			wgsLat:       40.7128, wgsLon: -74.0060, // New York
			gcjLat:       40.7128, gcjLon: -74.0060, // No GCJ-02 offset outside China
			expectedX:    123, expectedY: 456,
			description: "Should use WGS-84 coordinates for local directory",
		},
		{
			name:         "Google Maps low zoom level",
			provider:     dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
			x:            12, y: 34, z: 8,
			enableGcj02:  1,
			isOutOfChina: false,
			wgsLat:       39.9042, wgsLon: 116.4074,
			gcjLat:       39.9042 + 0.001, gcjLon: 116.4074 + 0.001,
			expectedX:    12, expectedY: 34,
			description: "Should use WGS-84 coordinates for local directory",
		},
		{
			name:         "Tianditu Local Directory",
			provider:     dbproto.MapProviderEnum_ProviderTiandituLocalDirectory,
			x:            123, y: 456, z: 10,
			enableGcj02:  1,
			isOutOfChina: false,
			wgsLat:       39.9042, wgsLon: 116.4074,
			gcjLat:       39.9042 + 0.001, gcjLon: 116.4074 + 0.001,
			expectedX:    123, expectedY: 456,
			description: "Should use WGS-84 coordinates for local directory",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create test token
			token := &MapProviderToken{
				Provider: tt.provider,
			}

			// Create test map request
			mapReq := &MapReq{
				X:            tt.x,
				Y:            tt.y,
				Z:            tt.z,
				EnableGcj02:  tt.enableGcj02,
				IsOutOfChina: tt.isOutOfChina,
				WgsLat:       tt.wgsLat,
				WgsLon:       tt.wgsLon,
				GcjLat:       tt.gcjLat,
				GcjLon:       tt.gcjLon,
			}

			// Calculate tile coordinates
			actualX, actualY := calculateLocalDirectoryTileCoordinates(token, mapReq)

			// For GCJ-02 conversion cases, we need to calculate expected coordinates
			if tt.expectedX == -1 && tt.expectedY == -1 {
				// This is a GCJ-02 conversion case, calculate expected WGS-84 coordinates
				wgsLat, wgsLon := GCJtoWGS(tt.gcjLat, tt.gcjLon)
				wgsPx, wgsPy := GlobalMapsManager.Mercator.LatLonToPixels(wgsLat, wgsLon, tt.z)
				wgsTx, wgsTy := GlobalMapsManager.Mercator.PixelsToTiles(wgsPx, wgsPy, tt.z)
				expectedX, expectedY := GlobalMapsManager.Mercator.GoogleTiles(wgsTx, wgsTy, tt.z)

				if actualX != expectedX || actualY != expectedY {
					t.Errorf("Expected converted coordinates (%d,%d), got (%d,%d)",
						expectedX, expectedY, actualX, actualY)
				}
				t.Logf("Test case: %s - GCJ-02 converted to WGS-84: (%d,%d)", tt.description, actualX, actualY)
			} else {
				// Normal case, use original coordinates
				if actualX != tt.expectedX || actualY != tt.expectedY {
					t.Errorf("Expected coordinates (%d,%d), got (%d,%d)",
						tt.expectedX, tt.expectedY, actualX, actualY)
				}
				t.Logf("Test case: %s - Result: (%d,%d)", tt.description, actualX, actualY)
			}
		})
	}
}

func TestShouldUseGcj02ForLocalDirectory(t *testing.T) {
	tests := []struct {
		name         string
		z            int
		enableGcj02  int
		isOutOfChina bool
		expected     bool
	}{
		{"High zoom in China with GCJ-02 enabled", 10, 1, false, true},
		{"High zoom in China with GCJ-02 disabled", 10, 0, false, false},
		{"High zoom outside China with GCJ-02 enabled", 10, 1, true, false},
		{"Low zoom in China with GCJ-02 enabled", 8, 1, false, false},
		{"Edge case: zoom level 9", 9, 1, false, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mapReq := &MapReq{
				Z:            tt.z,
				EnableGcj02:  tt.enableGcj02,
				IsOutOfChina: tt.isOutOfChina,
			}

			result := shouldUseGcj02ForLocalDirectory(mapReq)
			if result != tt.expected {
				t.Errorf("Expected %v, got %v", tt.expected, result)
			}
		})
	}
}

func TestBuildLocalFilePathWGS84(t *testing.T) {
	// Initialize GlobalMapsManager for testing
	if GlobalMapsManager.Mercator == nil {
		err := GlobalMapsManager.Init()
		if err != nil {
			t.Fatalf("Failed to initialize GlobalMapsManager: %v", err)
		}
	}

	tests := []struct {
		name        string
		baseUrl     string
		provider    dbproto.MapProviderEnum
		mapType     string
		x, y, z     int
		enableGcj02 int
		isOutOfChina bool
		expectPath  bool // Whether we expect a valid path
	}{
		{
			name:        "Google roadmap with valid base URL",
			baseUrl:     "/path/to/tiles",
			provider:    dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
			mapType:     MapTypeRoadmap,
			x: 123, y: 456, z: 10,
			enableGcj02:  1,
			isOutOfChina: false,
			expectPath:   true,
		},
		{
			name:        "Empty base URL",
			baseUrl:     "",
			provider:    dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
			mapType:     MapTypeRoadmap,
			x: 123, y: 456, z: 10,
			expectPath:  false,
		},
		{
			name:        "Satellite map type (should use jpg)",
			baseUrl:     "/path/to/tiles/",
			provider:    dbproto.MapProviderEnum_ProviderGoogleLocalDirectory,
			mapType:     MapTypeSatellite,
			x: 123, y: 456, z: 10,
			expectPath:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			token := &MapProviderToken{
				BaseUrl:  tt.baseUrl,
				Provider: tt.provider,
			}

			mapReq := &MapReq{
				X:            tt.x,
				Y:            tt.y,
				Z:            tt.z,
				MapType:      tt.mapType,
				EnableGcj02:  tt.enableGcj02,
				IsOutOfChina: tt.isOutOfChina,
				WgsLat:       39.9042,
				WgsLon:       116.4074,
				GcjLat:       39.9042 + 0.001,
				GcjLon:       116.4074 + 0.001,
			}

			filePath := buildLocalFilePath(token, mapReq)

			if tt.expectPath {
				if filePath == "" {
					t.Errorf("Expected valid file path, got empty string")
				} else {
					t.Logf("Generated file path: %s", filePath)

					// Verify file extension
					if tt.mapType == MapTypeSatellite || tt.mapType == MapTypeHybrid {
						if !contains(filePath, ".jpg") {
							t.Errorf("Expected .jpg extension for satellite/hybrid maps, got: %s", filePath)
						}
					} else {
						if !contains(filePath, ".png") {
							t.Errorf("Expected .png extension for roadmap, got: %s", filePath)
						}
					}
				}
			} else {
				if filePath != "" {
					t.Errorf("Expected empty file path, got: %s", filePath)
				}
			}
		})
	}
}

// Helper function to check if string contains substring
func contains(s, substr string) bool {
	return len(s) >= len(substr) && s[len(s)-len(substr):] == substr
}
