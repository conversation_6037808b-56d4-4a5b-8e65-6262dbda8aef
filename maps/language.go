package maps

import (
	"bfmap/config"
	"bfmap/dbproto"
	"fmt"
	"log/slog"

	"golang.org/x/text/language"
)

// NormalizeLanguageCode standardizes the language code to BCP 47 format
func NormalizeLanguageCode(lang string) (string, error) {
	if lang == "" {
		return "", nil
	}

	// Parse the language tag
	tag, err := language.Parse(lang)
	if err != nil {
		return "", fmt.Errorf("invalid language code: %w", err)
	}

	// Standardize to BCP 47 format
	return tag.String(), nil
}

// normalizeChinese standardizes Chinese language codes
// Uses the standard library to handle Chinese language variants and normalizes them to the standard format
func normalizeChinese(lang string) string {
	// First, use the standard library to parse the language tag
	tag, err := language.Parse(lang)
	if err != nil {
		// If parsing fails, use a simple mapping as a fallback
		switch lang {
		case "zh", "zh_CN", "zh-CN":
			return "zh-CN"
		default:
			return lang
		}
	}

	// For Chinese, ensure the use of zh-CN format
	if base, _ := tag.Base(); base.String() == "zh" {
		// For Chinese, always return zh-CN format
		// Explicitly construct zh-CN instead of relying on the original input format
		return "zh-CN"
	}

	// For non-Chinese languages, return the standardized format
	return tag.String()
}

// matchLanguage performs language matching, supporting exact and language family matches
func matchLanguage(requestLang, tokenLang string) bool {
	// Standardize the token language code
	tokLang, err := NormalizeLanguageCode(tokenLang)
	if err != nil {
		if config.IsVerboseDebugMap {
			slog.Warn("failed to normalize token language code",
				"tokenLang", tokenLang,
				"err", err)
		}
		return false
	}

	// Exact match
	if requestLang == tokLang {
		return true
	}

	// Language family match
	if requestLang == "" || tokLang == "" {
		return false
	}

	// Parse the already standardized language codes
	reqTag, err := language.Parse(requestLang)
	if err != nil {
		if config.IsVerboseDebugMap {
			slog.Warn("failed to parse request language for family matching",
				"requestLang", requestLang,
				"err", err)
		}
		return false
	}

	tokTag, err := language.Parse(tokLang)
	if err != nil {
		if config.IsVerboseDebugMap {
			slog.Warn("failed to parse token language for family matching",
				"tokenLang", tokLang,
				"err", err)
		}
		return false
	}

	// Compare language families (remove region info)
	reqBase, _ := reqTag.Base()
	tokBase, _ := tokTag.Base()

	return reqBase == tokBase
}

// matchTiandituLanguage implements Tianditu-specific language matching logic
// Only supports Chinese, using the standard library to handle language variants
func matchTiandituLanguage(requestLang, tokenLang string) bool {
	normalizedReqLang := normalizeChinese(requestLang)
	normalizedTokenLang := normalizeChinese(tokenLang)

	return normalizedReqLang == "zh-CN" && normalizedTokenLang == "zh-CN"
}

// isLanguageCompatible checks whether the request language and token language are compatible
// Executes the corresponding matching logic according to different provider types and map types
func isLanguageCompatible(requestLang, tokenLang string, providerType dbproto.MapProviderEnum, mapType string) bool {
	// Backward compatibility: empty token language matches all requests
	if tokenLang == "" {
		return true
	}

	// Skip language check for satellite maps, as they usually do not contain text
	if mapType == MapTypeSatellite {
		return true
	}

	// Execute specific matching logic according to provider type
	switch providerType {
	case dbproto.MapProviderEnum_ProviderGoogleLocalDirectory:
		// Google supports multi-language matching
		return matchLanguage(requestLang, tokenLang)

	case dbproto.MapProviderEnum_ProviderTiandituLocalDirectory:
		// Tianditu only supports Chinese
		return matchTiandituLanguage(requestLang, tokenLang)

	case dbproto.MapProviderEnum_ProviderOSMLocalDirectory:
		// OSM skips language matching, language is determined by geographic location
		return true

	default:
		// Unknown provider type, default to incompatible
		if config.IsVerboseDebugMap {
			slog.Warn("unknown provider type for language compatibility check",
				"providerType", providerType)
		}
		return false
	}
}
