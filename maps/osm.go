package maps

import (
	"bfmap/config"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// https://tile.openstreetmap.org/{zoom}/{x}/{y}.png
const OSMMapTileApiUrlTemplate = "%s/%d/%d/%d.png"

func ReqMapTileFromOSM(
	x, y, z int,
	token string,
	BaseUrl string,
	needProxy bool) ([]byte, error) {
	if len(BaseUrl) == 0 {
		BaseUrl = config.DefaultOSMMapStaticApiBaseUrl
	} else {
		BaseUrl = strings.TrimSuffix(BaseUrl, "/")
	}

	urlStr := fmt.Sprintf(
		OSMMapTileApiUrlTemplate,
		BaseUrl, z, x, y,
	)

	// add token to url
	if len(token) > 0 {
		urlStr = fmt.Sprintf("%s?tk=%s", urlStr, url.QueryEscape(token))
	}

	if config.IsVerboseDebugMap {
		slog.Debug("req osm map tile", "url", urlStr)
	}

	httpClient := GetMapHttpClient(10*time.Second, needProxy)

	req, err := http.NewRequest(http.MethodGet, urlStr, nil)
	if err != nil {
		return nil, fmt.Errorf("new http request failed, %w", err)
	}
	// TODO: set user agent
	req.Header.Set(
		"User-Agent",
		"curl/8.12.1")

	resp, err := httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("get map tile from osm failed, %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		data, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf(
			"get map layer from osm but %w, %d,%s",
			ErrHttpRespondIsNotOK,
			resp.StatusCode,
			string(data),
		)
	}

	return io.ReadAll(resp.Body)
}

func ReqMapTileFromDefaultOSM(x, y, z int) ([]byte, error) {
	return ReqMapTileFromOSM(x, y, z, "", "", true)
}
