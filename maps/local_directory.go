package maps

import (
	"bfmap/config"
	"bfmap/dbproto"
	"fmt"
	"log/slog"
	"os"
	"path/filepath"
	"strconv"
)

// Local Directory Structure:
//
// Local directory tiles are stored using WGS-84 coordinate system.
// This means the file paths always use the original tile coordinates (mapReq.X, mapReq.Y)
// regardless of the gcj02 parameter or geographic location.
//
// Directory structure:
// {BaseUrl}/{mapType}/{z}/{x}/{y}.{ext}
// Where x,y are always the original WGS-84 tile coordinates
//
// Example:
// - Request: x=123, y=456, z=10, gcj02=1, in China
// - Local directory lookup: /path/to/tiles/roadmap/10/123/456.png (WGS-84 coordinates)
// - If local fails, online service will handle GCJ-02 conversion internally
//
// This approach ensures:
// 1. Consistent local directory structure regardless of coordinate system
// 2. Online service fallback handles GCJ-02 conversion when needed
// 3. No coordinate system confusion in local storage

// getLocalDirectoryProviderType maps online provider type to corresponding local directory provider type
func getLocalDirectoryProviderType(onlineProviderType int) int {
	switch onlineProviderType {
	case int(dbproto.MapProviderEnum_ProviderGoogle):
		return int(dbproto.MapProviderEnum_ProviderGoogleLocalDirectory)
	case int(dbproto.MapProviderEnum_ProviderTianditu):
		return int(dbproto.MapProviderEnum_ProviderTiandituLocalDirectory)
	case int(dbproto.MapProviderEnum_ProviderOSM):
		return int(dbproto.MapProviderEnum_ProviderOSMLocalDirectory)
	default:
		// Unsupported provider type
		return -1
	}
}

// calculateLocalDirectoryTileCoordinates calculates tile coordinates for local directory
// Local directory tiles are stored using WGS-84 coordinates, so we always use original coordinates
func calculateLocalDirectoryTileCoordinates(token *MapProviderToken, mapReq *MapReq) (tileX, tileY int) {
	// Local directory tiles are stored using WGS-84 coordinate system
	// Always use original coordinates for local directory lookup
	if config.IsVerboseDebugMap {
		slog.Debug("using WGS-84 coordinates for local directory",
			"provider", token.Provider,
			"originalX", mapReq.X,
			"originalY", mapReq.Y,
			"enableGcj02", mapReq.EnableGcj02,
			"isOutOfChina", mapReq.IsOutOfChina)
	}

	return mapReq.X, mapReq.Y
}



// filterCompatibleTokens filters provider tokens that are language compatible
func filterCompatibleTokens(tokens []*MapProviderToken, mapReq *MapReq) []*MapProviderToken {
	var compatibleTokens []*MapProviderToken

	for _, token := range tokens {
		// Check token basic validity
		if !token.IsValid() {
			continue
		}

		// Check zoom range
		if token.MinZoom > 0 && mapReq.Z < token.MinZoom {
			continue
		}
		if token.MaxZoom > 0 && mapReq.Z > token.MaxZoom {
			continue
		}

		// Use Language field
		tokenLanguage := token.Language

		// Check language compatibility
		if isLanguageCompatible(mapReq.Lang, tokenLanguage, token.Provider, mapReq.MapType) {
			compatibleTokens = append(compatibleTokens, token)

			if config.IsVerboseDebugMap {
				slog.Debug("found language-compatible token",
					"tokenRid", token.TokenRid,
					"requestLang", mapReq.Lang,
					"tokenLang", tokenLanguage,
					"provider", token.Provider,
					"mapType", mapReq.MapType)
			}
		} else {
			if config.IsVerboseDebugMap {
				slog.Debug("token language not compatible",
					"tokenRid", token.TokenRid,
					"requestLang", mapReq.Lang,
					"tokenLang", tokenLanguage,
					"provider", token.Provider,
					"mapType", mapReq.MapType)
			}
		}
	}

	return compatibleTokens
}

// buildLocalFilePath builds local file path with GCJ-02 coordinate support
func buildLocalFilePath(token *MapProviderToken, mapReq *MapReq) string {
	baseUrl := token.BaseUrl
	if baseUrl == "" {
		return ""
	}

	// Remove trailing slash
	if baseUrl[len(baseUrl)-1] == '/' {
		baseUrl = baseUrl[:len(baseUrl)-1]
	}

	// Determine file extension
	ext := "png"
	if mapReq.MapType == MapTypeSatellite || mapReq.MapType == MapTypeHybrid {
		ext = "jpg"
	}

	// Calculate tile coordinates based on coordinate system
	tileX, tileY := calculateLocalDirectoryTileCoordinates(token, mapReq)

	// Build file path: {BaseUrl}/{mapType}/{z}/{x}/{y}.{ext}
	return filepath.Join(
		baseUrl,
		mapReq.MapType,
		strconv.Itoa(mapReq.Z),
		strconv.Itoa(tileX),
		fmt.Sprintf("%d.%s", tileY, ext),
	)
}

// readLocalDirectoryFile reads file from local directory
func readLocalDirectoryFile(token *MapProviderToken, mapReq *MapReq) ([]byte, error) {
	filePath := buildLocalFilePath(token, mapReq)
	if filePath == "" {
		return nil, fmt.Errorf("invalid file path")
	}

	// Read file
	imageBytes, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file %s: %w", filePath, err)
	}

	if len(imageBytes) == 0 {
		return nil, fmt.Errorf("file %s is empty", filePath)
	}

	return imageBytes, nil
}

// QueryTileFromLocalDirectory queries map tiles from local directory with GCJ-02 support
func QueryTileFromLocalDirectory(
	userRid string,
	mapReq *MapReq,
	localProviderType int,
	needCreateNewIndex bool,
) (tileInfo *TileInfo, imageBytes []byte, err error) {
	if config.IsVerboseDebugMap {
		slog.Debug("QueryTileFromLocalDirectory started",
			"userRid", userRid,
			"localProviderType", localProviderType,
			"mapReq", mapReq.Key(),
			"enableGcj02", mapReq.EnableGcj02,
			"isOutOfChina", mapReq.IsOutOfChina,
			"zoom", mapReq.Z)
	}

	// Get local directory provider tokens
	providerTokens, err := GlobalMapsManager.GetMapProviderTokens(userRid, localProviderType)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get local directory provider tokens: %w", err)
	}

	if len(providerTokens) == 0 {
		return nil, nil, fmt.Errorf("no local directory provider tokens found for provider type %d", localProviderType)
	}

	// Filter language compatible tokens
	compatibleTokens := filterCompatibleTokens(providerTokens, mapReq)
	if len(compatibleTokens) == 0 {
		return nil, nil, fmt.Errorf("no language-compatible local directory provider token found")
	}

	// Try to read file from compatible tokens
	for _, token := range compatibleTokens {
		// Build file path with coordinate system consideration
		filePath := buildLocalFilePath(token, mapReq)
		if filePath == "" {
			if config.IsVerboseDebugMap {
				slog.Debug("invalid file path for token",
					"tokenRid", token.TokenRid,
					"baseUrl", token.BaseUrl)
			}
			continue
		}

		// Log coordinate transformation details for Google Maps
		if token.Provider == dbproto.MapProviderEnum_ProviderGoogleLocalDirectory && config.IsVerboseDebugMap {
			tileX, tileY := calculateLocalDirectoryTileCoordinates(token, mapReq)
			slog.Debug("local directory coordinate calculation",
				"tokenRid", token.TokenRid,
				"originalX", mapReq.X,
				"originalY", mapReq.Y,
				"calculatedX", tileX,
				"calculatedY", tileY,
				"coordinateSystem", "WGS-84",
				"filePath", filePath)
		}

		// Check if file exists
		if _, err := os.Stat(filePath); err != nil {
			if config.IsVerboseDebugMap {
				slog.Debug("local directory file does not exist",
					"filePath", filePath,
					"tokenRid", token.TokenRid)
			}
			continue
		}

		// Read file
		imageBytes, err = os.ReadFile(filePath)
		if err != nil {
			if config.IsVerboseDebugMap {
				slog.Debug("failed to read local directory file",
					"filePath", filePath,
					"tokenRid", token.TokenRid,
					"err", err)
			}
			continue
		}

		// Check if file is empty
		if len(imageBytes) == 0 {
			if config.IsVerboseDebugMap {
				slog.Debug("local directory file is empty",
					"filePath", filePath,
					"tokenRid", token.TokenRid)
			}
			continue
		}

		// Successfully read file
		if config.IsVerboseDebugMap {
			slog.Debug("successfully read local directory file",
				"filePath", filePath,
				"tokenRid", token.TokenRid,
				"fileSize", len(imageBytes))
		}

		// Save to database and create TileInfo
		tileInfo, err = SaveTileToDb(mapReq, imageBytes, needCreateNewIndex)
		if err != nil {
			slog.Warn("QueryTileFromLocalDirectory SaveTileToDb fail", "req", mapReq.Key(), "err", err)
			// Return image data even if save fails
			tileInfo = &TileInfo{
				Hash:      "",
				CacheTime: 0,
				Status:    1,
			}
		}

		return tileInfo, imageBytes, nil
	}

	return nil, nil, fmt.Errorf("no local directory file found for request")
}
