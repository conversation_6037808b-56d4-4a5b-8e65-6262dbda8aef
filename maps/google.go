package maps

import (
	"bfmap/bfutil"
	"bfmap/config"
	"bytes"
	"encoding/json"
	"fmt"
	"image"
	"io"
	"log/slog"
	"net/http"
	"strconv"
	"strings"
	"time"

	"golang.org/x/image/draw"
)

// nolint
const GoogleMapStaticApiUrlTemplate = "%s/maps/api/staticmap?size=640x640&scale=1&maptype=%s&format=%s&zoom=%d&center=%s&key=%s&language=%s"

// https://tile.googleapis.com/v1/2dtiles/z/x/y?session=YOUR_SESSION_ID&key=YOUR_API_KEY
const GoogleMapTileApiUrlTemplate = "%s/v1/2dtiles/%d/%d/%d?session=%s&key=%s"

// https://tile.googleapis.com/v1/createSession?key=YOUR_API_KEY
const GoogleMapTileSessionUrlTemplate = "%s/v1/createSession?key=%s"

// ReqMapTileFromGoogleMapStaticReturnOne get map tile from google map static.
// imageFormat: png or jpg.
// mtype: roadmap, satellite, hybrid.
// socks5Proxy: default not use, use when request failed.
// return image size:640x640
func ReqMapTileFromGoogleMapStatic(
	providerToken string,
	mapType string,
	lat, lon float64,
	zoom int,
	lang,
	imageFormat string,
	needHybridMarker bool,
	baseUrl string,
	needProxy bool) ([]byte, error) {
	if imageFormat == TileImageFormatJpg {
		imageFormat = TileImageFormatJpgBaseline
	}

	center := fmt.Sprintf("%.10f,%.10f", lat, lon)

	if mapType == MapTypeHybrid && !needHybridMarker {
		mapType = MapTypeSatellite
	}

	if baseUrl == "" {
		baseUrl = config.DefaultMapStaticApiBaseUrl
	} else {
		baseUrl = strings.TrimSuffix(baseUrl, "/")
	}

	urlStr := fmt.Sprintf(
		GoogleMapStaticApiUrlTemplate,
		baseUrl,
		mapType,
		imageFormat,
		zoom,
		center,
		providerToken,
		lang,
	)

	if config.IsVerboseDebugMap {
		slog.Debug("req google map static", "url", urlStr)
	}

	httpClient := GetMapHttpClient(10*time.Second, needProxy)
	resp, err := httpClient.Get(urlStr)
	if err != nil {
		return nil, fmt.Errorf("get map tile from google map failed, %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		data, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf(
			"get map layer from google map static but %w, %d,%s",
			ErrHttpRespondIsNotOK,
			resp.StatusCode,
			string(data),
		)
	}

	return io.ReadAll(resp.Body)
}

type GoogleMapTileSessionReq struct {
	// roadmap, satellite
	MapType  string `json:"mapType"`
	Language string `json:"language"`
	Region   string `json:"region"`
	// jpeg,png
	ImageFormat string `json:"imageFormat,omitempty"`
	// scaleFactor1x, scaleFactor2x, scaleFactor4x
	Scale   string `json:"scale,omitempty"`
	HighDpi bool   `json:"highDpi,omitempty"`
	//layerRoadmap,layerStreetview,layerTraffic
	LayerTypes []string `json:"layerTypes,omitempty"`
	Overlay    bool     `json:"overlay,omitempty"`
}

type GoogleMapTileSessionResp struct {
	Session     string `json:"session"`
	Expiry      string `json:"expiry"`
	TileWidth   int    `json:"tileWidth"`
	TileHeight  int    `json:"tileHeight"`
	ImageFormat string `json:"imageFormat"`
	ExpiryTime  time.Time
}

// ReqMapTileSessionFromGoogleMap get map tile session from google map.
// imageFormat: png or jpeg.
func ReqMapTileSessionFromGoogleMap(
	providerToken,
	mapType,
	language,
	region,
	imageFormat string,
	needHybridMarker bool,
	baseUrl string,
	needProxy bool) (*GoogleMapTileSessionResp, error) {
	if imageFormat == TileImageFormatJpg || imageFormat == TileImageFormatJpgBaseline {
		imageFormat = "jpeg"
	}
	sessionReq := &GoogleMapTileSessionReq{
		MapType:     mapType,
		Language:    language,
		Region:      region,
		ImageFormat: imageFormat,
		Scale:       "scaleFactor1x",
	}
	if mapType == MapTypeHybrid {
		sessionReq.MapType = MapTypeSatellite
		if needHybridMarker {
			sessionReq.LayerTypes = []string{"layerRoadmap", "layerStreetview"}
			sessionReq.Overlay = false
		}
	}

	marshal, err := json.Marshal(sessionReq)
	if err != nil {
		return nil, fmt.Errorf("marshal map tile session request to json fail, %w", err)
	}

	if baseUrl == "" {
		baseUrl = config.DefaultMapTileApiBaseUrl
	} else {
		baseUrl = strings.TrimSuffix(baseUrl, "/")
	}

	urlStr := fmt.Sprintf(GoogleMapTileSessionUrlTemplate, baseUrl, providerToken)
	if config.IsVerboseDebugMap {
		slog.Debug("req google map tile session", "url", urlStr)
	}

	httpClient := GetMapHttpClient(10*time.Second, needProxy)

	resp, err := httpClient.Post(urlStr, "application/json", bytes.NewReader(marshal))
	if err != nil {
		return nil, fmt.Errorf("get map tile session request fail, %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		data, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf(
			"get google map tile session but %w, %d,%s",
			ErrHttpRespondIsNotOK,
			resp.StatusCode,
			string(data),
		)
	}

	sessionResp := &GoogleMapTileSessionResp{}
	err = json.NewDecoder(resp.Body).Decode(&sessionResp)
	if err != nil {
		return nil, fmt.Errorf("decode map tile session response fail, %w", err)
	}
	sec, err := strconv.ParseInt(sessionResp.Expiry, 10, 64)
	if err == nil {
		sessionResp.ExpiryTime = time.Unix(sec, 0)
	}

	if config.IsVerboseDebugMap {
		slog.Debug("get google map tile session", "session", sessionResp)
	}
	return sessionResp, nil
}

func ReqMapTileFromGoogleMapTile(
	session *GoogleMapTileSessionResp,
	providerToken string,
	x, y, z int,
	imageFormat string,
	baseUrl string,
	needProxy bool) ([]byte, error) {
	if baseUrl == "" {
		baseUrl = config.DefaultMapTileApiBaseUrl
	} else {
		baseUrl = strings.TrimSuffix(baseUrl, "/")
	}
	urlStr := fmt.Sprintf(
		GoogleMapTileApiUrlTemplate,
		baseUrl,
		z, x, y,
		session.Session,
		providerToken,
	)
	if config.IsVerboseDebugMap {
		slog.Debug("req google map tile", "url", urlStr)
	}

	httpClient := GetMapHttpClient(10*time.Second, needProxy)
	resp, err := httpClient.Get(urlStr)
	if err != nil {
		return nil, fmt.Errorf("get map tile from google map tile failed, %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		data, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf(
			"get map layer from google map tile but %w, %d,%s",
			ErrHttpRespondIsNotOK,
			resp.StatusCode,
			string(data),
		)
	}

	if session.TileWidth == 256 && session.TileHeight == 256 {
		data, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, fmt.Errorf("read map tile from google map tile failed, %w", err)
		}
		return data, nil
	}

	// resize to 256
	img, _, err := image.Decode(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("decode map tile from google map  failed, %w", err)
	}

	// resize to 256
	m := image.NewRGBA(image.Rect(0, 0, 256, 256))
	scaler := draw.BiLinear
	scaler.Scale(m, m.Bounds(), img, img.Bounds(), draw.Over, nil)
	img = m.SubImage(m.Bounds())

	data, err := bfutil.ConvertImage2Bytes(img, imageFormat)
	if err != nil {
		return nil, fmt.Errorf("encode map tile from google map failed, %w", err)
	}

	return data, nil
}
