package maps

import (
	"bfmap/dbproto"
	"testing"
)

func TestNormalizeLanguageCode(t *testing.T) {
	tests := []struct {
		input    string
		expected string
		hasError bool
	}{
		{"zh-CN", "zh-CN", false},
		{"zh", "zh", false},
		{"en-US", "en-US", false},
		{"en", "en", false},
		{"", "", false}, // 空字符串应该返回空字符串
		{"invalid-lang", "", true},
	}

	for _, test := range tests {
		result, err := NormalizeLanguageCode(test.input)
		if test.hasError {
			if err == nil {
				t.<PERSON>rf("Expected error for input %s, but got none", test.input)
			}
		} else {
			if err != nil {
				t.<PERSON>rf("Unexpected error for input %s: %v", test.input, err)
			}
			if result != test.expected {
				t.<PERSON>rf("For input %s, expected %s, got %s", test.input, test.expected, result)
			}
		}
	}
}

func TestNormalizeChinese(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"zh", "zh-CN"},
		{"zh_CN", "zh-CN"},
		{"zh-CN", "zh-CN"},
		{"en", "en"},
		{"en-US", "en-US"},
		{"", ""},
	}

	for _, test := range tests {
		result := normalizeChinese(test.input)
		if result != test.expected {
			t.Errorf("For input %s, expected %s, got %s", test.input, test.expected, result)
		}
	}
}

func TestMatchLanguage(t *testing.T) {
	tests := []struct {
		requestLang string
		tokenLang   string
		expected    bool
	}{
		{"zh-CN", "zh-CN", true},  // 精确匹配
		{"en-US", "en-US", true},  // 精确匹配
		{"zh-CN", "zh", true},     // 语言族匹配
		{"zh", "zh-CN", true},     // 语言族匹配
		{"en-US", "en", true},     // 语言族匹配
		{"en", "en-US", true},     // 语言族匹配
		{"zh-CN", "en-US", false}, // 不匹配
		{"", "", true},            // 空字符串匹配
	}

	for _, test := range tests {
		result := matchLanguage(test.requestLang, test.tokenLang)
		if result != test.expected {
			t.Errorf("For requestLang=%s, tokenLang=%s, expected %v, got %v",
				test.requestLang, test.tokenLang, test.expected, result)
		}
	}
}

func TestMatchTiandituLanguage(t *testing.T) {
	tests := []struct {
		requestLang string
		tokenLang   string
		expected    bool
	}{
		{"zh-CN", "zh-CN", true},
		{"zh", "zh-CN", true},
		{"zh_CN", "zh-CN", true},
		{"zh-CN", "zh", true},
		{"zh", "zh", true},
		{"en-US", "zh-CN", false},
		{"zh-CN", "en-US", false},
		{"", "", false}, // 天地图不支持空语言
	}

	for _, test := range tests {
		result := matchTiandituLanguage(test.requestLang, test.tokenLang)
		if result != test.expected {
			t.Errorf("For requestLang=%s, tokenLang=%s, expected %v, got %v",
				test.requestLang, test.tokenLang, test.expected, result)
		}
	}
}

func TestIsLanguageCompatible(t *testing.T) {
	tests := []struct {
		requestLang  string
		tokenLang    string
		providerType dbproto.MapProviderEnum
		mapType      string
		expected     bool
	}{
		// 向后兼容：空token语言匹配所有请求
		{"zh-CN", "", dbproto.MapProviderEnum_ProviderGoogleLocalDirectory, MapTypeRoadmap, true},
		{"en-US", "", dbproto.MapProviderEnum_ProviderTiandituLocalDirectory, MapTypeRoadmap, true},

		// 卫星图跳过语言检查
		{"zh-CN", "en-US", dbproto.MapProviderEnum_ProviderGoogleLocalDirectory, MapTypeSatellite, true},
		{"en-US", "zh-CN", dbproto.MapProviderEnum_ProviderTiandituLocalDirectory, MapTypeSatellite, true},

		// Google provider语言匹配
		{"zh-CN", "zh-CN", dbproto.MapProviderEnum_ProviderGoogleLocalDirectory, MapTypeRoadmap, true},
		{"zh-CN", "zh", dbproto.MapProviderEnum_ProviderGoogleLocalDirectory, MapTypeRoadmap, true},
		{"zh-CN", "en-US", dbproto.MapProviderEnum_ProviderGoogleLocalDirectory, MapTypeRoadmap, false},

		// 天地图语言匹配（仅支持中文）
		{"zh-CN", "zh-CN", dbproto.MapProviderEnum_ProviderTiandituLocalDirectory, MapTypeRoadmap, true},
		{"zh", "zh-CN", dbproto.MapProviderEnum_ProviderTiandituLocalDirectory, MapTypeRoadmap, true},
		{"en-US", "zh-CN", dbproto.MapProviderEnum_ProviderTiandituLocalDirectory, MapTypeRoadmap, false},

		// OSM跳过语言检查
		{"zh-CN", "en-US", dbproto.MapProviderEnum_ProviderOSMLocalDirectory, MapTypeRoadmap, true},
		{"en-US", "zh-CN", dbproto.MapProviderEnum_ProviderOSMLocalDirectory, MapTypeRoadmap, true},
	}

	for _, test := range tests {
		result := isLanguageCompatible(test.requestLang, test.tokenLang, test.providerType, test.mapType)
		if result != test.expected {
			t.Errorf("For requestLang=%s, tokenLang=%s, provider=%v, mapType=%s, expected %v, got %v",
				test.requestLang, test.tokenLang, test.providerType, test.mapType, test.expected, result)
		}
	}
}

func TestGetLocalDirectoryProviderType(t *testing.T) {
	tests := []struct {
		onlineType int
		expected   int
	}{
		{int(dbproto.MapProviderEnum_ProviderGoogle), int(dbproto.MapProviderEnum_ProviderGoogleLocalDirectory)},
		{int(dbproto.MapProviderEnum_ProviderTianditu), int(dbproto.MapProviderEnum_ProviderTiandituLocalDirectory)},
		{int(dbproto.MapProviderEnum_ProviderOSM), int(dbproto.MapProviderEnum_ProviderOSMLocalDirectory)},
		{999, -1}, // 不支持的provider类型
	}

	for _, test := range tests {
		result := getLocalDirectoryProviderType(test.onlineType)
		if result != test.expected {
			t.Errorf("For onlineType=%d, expected %d, got %d", test.onlineType, test.expected, result)
		}
	}
}

func TestBuildLocalFilePath(t *testing.T) {
	token := &MapProviderToken{
		BaseUrl: "/path/to/tiles",
	}

	tests := []struct {
		mapReq   *MapReq
		expected string
	}{
		{
			&MapReq{X: 1, Y: 2, Z: 3, MapType: MapTypeRoadmap},
			"/path/to/tiles/roadmap/3/1/2.png",
		},
		{
			&MapReq{X: 4, Y: 5, Z: 6, MapType: MapTypeSatellite},
			"/path/to/tiles/satellite/6/4/5.jpg",
		},
		{
			&MapReq{X: 7, Y: 8, Z: 9, MapType: MapTypeHybrid},
			"/path/to/tiles/hybrid/9/7/8.jpg",
		},
	}

	for _, test := range tests {
		result := buildLocalFilePath(token, test.mapReq)
		if result != test.expected {
			t.Errorf("For mapReq=%+v, expected %s, got %s", test.mapReq, test.expected, result)
		}
	}

	// 测试空BaseUrl
	emptyToken := &MapProviderToken{BaseUrl: ""}
	result := buildLocalFilePath(emptyToken, &MapReq{X: 1, Y: 2, Z: 3, MapType: MapTypeRoadmap})
	if result != "" {
		t.Errorf("Expected empty string for empty BaseUrl, got %s", result)
	}

	// 测试带末尾斜杠的BaseUrl
	slashToken := &MapProviderToken{BaseUrl: "/path/to/tiles/"}
	result = buildLocalFilePath(slashToken, &MapReq{X: 1, Y: 2, Z: 3, MapType: MapTypeRoadmap})
	expected := "/path/to/tiles/roadmap/3/1/2.png"
	if result != expected {
		t.Errorf("For BaseUrl with trailing slash, expected %s, got %s", expected, result)
	}
}

// 简化的语言兼容性测试，不依赖IsValid方法
func TestLanguageCompatibilityLogic(t *testing.T) {
	tests := []struct {
		tokenLang   string
		requestLang string
		provider    dbproto.MapProviderEnum
		mapType     string
		shouldMatch bool
	}{
		// Google provider测试
		{"zh-CN", "zh-CN", dbproto.MapProviderEnum_ProviderGoogleLocalDirectory, MapTypeRoadmap, true},
		{"zh", "zh-CN", dbproto.MapProviderEnum_ProviderGoogleLocalDirectory, MapTypeRoadmap, true},
		{"en-US", "zh-CN", dbproto.MapProviderEnum_ProviderGoogleLocalDirectory, MapTypeRoadmap, false},
		{"", "zh-CN", dbproto.MapProviderEnum_ProviderGoogleLocalDirectory, MapTypeRoadmap, true}, // 空语言匹配所有

		// 天地图provider测试
		{"zh-CN", "zh-CN", dbproto.MapProviderEnum_ProviderTiandituLocalDirectory, MapTypeRoadmap, true},
		{"zh", "zh-CN", dbproto.MapProviderEnum_ProviderTiandituLocalDirectory, MapTypeRoadmap, true},
		{"en-US", "zh-CN", dbproto.MapProviderEnum_ProviderTiandituLocalDirectory, MapTypeRoadmap, false},

		// OSM provider测试（跳过语言检查）
		{"en-US", "zh-CN", dbproto.MapProviderEnum_ProviderOSMLocalDirectory, MapTypeRoadmap, true},
		{"zh-CN", "en-US", dbproto.MapProviderEnum_ProviderOSMLocalDirectory, MapTypeRoadmap, true},

		// 卫星图测试（跳过语言检查）
		{"en-US", "zh-CN", dbproto.MapProviderEnum_ProviderGoogleLocalDirectory, MapTypeSatellite, true},
		{"zh-CN", "en-US", dbproto.MapProviderEnum_ProviderTiandituLocalDirectory, MapTypeSatellite, true},
	}

	for _, test := range tests {
		result := isLanguageCompatible(test.requestLang, test.tokenLang, test.provider, test.mapType)
		if result != test.shouldMatch {
			t.Errorf(
				"Language compatibility test failed: requestLang=%s, tokenLang=%s, provider=%v, mapType=%s, expected=%v, got=%v",
				test.requestLang,
				test.tokenLang,
				test.provider,
				test.mapType,
				test.shouldMatch,
				result,
			)
		}
	}
}

// 测试本地文件路径构建的边界情况
func TestBuildLocalFilePathEdgeCases(t *testing.T) {
	// 测试不同的BaseUrl格式
	tests := []struct {
		baseUrl  string
		mapReq   *MapReq
		expected string
	}{
		{
			"/path/to/tiles",
			&MapReq{X: 1, Y: 2, Z: 3, MapType: MapTypeRoadmap},
			"/path/to/tiles/roadmap/3/1/2.png",
		},
		{
			"/path/to/tiles/", // 带末尾斜杠
			&MapReq{X: 1, Y: 2, Z: 3, MapType: MapTypeRoadmap},
			"/path/to/tiles/roadmap/3/1/2.png",
		},
		{
			"", // 空路径
			&MapReq{X: 1, Y: 2, Z: 3, MapType: MapTypeRoadmap},
			"",
		},
	}

	for _, test := range tests {
		token := &MapProviderToken{BaseUrl: test.baseUrl}
		result := buildLocalFilePath(token, test.mapReq)
		if result != test.expected {
			t.Errorf("For baseUrl=%s, expected %s, got %s", test.baseUrl, test.expected, result)
		}
	}
}

// 测试provider类型映射的完整性
func TestProviderTypeMappingCompleteness(t *testing.T) {
	// 确保所有在线provider都有对应的本地目录provider
	onlineProviders := []int{
		int(dbproto.MapProviderEnum_ProviderGoogle),
		int(dbproto.MapProviderEnum_ProviderTianditu),
		int(dbproto.MapProviderEnum_ProviderOSM),
	}

	expectedLocalProviders := []int{
		int(dbproto.MapProviderEnum_ProviderGoogleLocalDirectory),
		int(dbproto.MapProviderEnum_ProviderTiandituLocalDirectory),
		int(dbproto.MapProviderEnum_ProviderOSMLocalDirectory),
	}

	for i, onlineProvider := range onlineProviders {
		localProvider := getLocalDirectoryProviderType(onlineProvider)
		if localProvider != expectedLocalProviders[i] {
			t.Errorf("For online provider %d, expected local provider %d, got %d",
				onlineProvider, expectedLocalProviders[i], localProvider)
		}
	}

	// 测试不支持的provider类型
	unsupportedProvider := 999
	result := getLocalDirectoryProviderType(unsupportedProvider)
	if result != -1 {
		t.Errorf("For unsupported provider %d, expected -1, got %d", unsupportedProvider, result)
	}
}
